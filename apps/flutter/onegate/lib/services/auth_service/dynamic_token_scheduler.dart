import 'dart:async';
import 'dart:developer';
import 'package:flutter_onegate/services/auth_service/jwt_token_utility.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_token_refresh_manager.dart';

/// Central coordinator for dynamic token expiration handling
/// This service manages automatic token refresh scheduling based on actual JWT token lifetimes
class DynamicTokenScheduler {
  static final DynamicTokenScheduler _instance = DynamicTokenScheduler._internal();
  factory DynamicTokenScheduler() => _instance;
  DynamicTokenScheduler._internal();

  // Dependencies
  EnhancedTokenRefreshManager? _tokenRefreshManager;
  
  // State management
  bool _isInitialized = false;
  bool _isActive = false;
  String? _currentAccessToken;
  String? _currentRefreshToken;
  
  // Dynamic scheduling variables
  Timer? _accessTokenRefreshTimer;
  Timer? _refreshTokenRefreshTimer;
  Timer? _monitoringTimer;
  DateTime? _nextAccessTokenRefresh;
  DateTime? _nextRefreshTokenRefresh;
  
  // Configuration
  static const Duration _monitoringInterval = Duration(seconds: 30);
  static const Duration _minimumRefreshBuffer = Duration(seconds: 30);
  
  /// Initialize the dynamic token scheduler
  Future<void> initialize(EnhancedTokenRefreshManager tokenRefreshManager) async {
    if (_isInitialized) return;
    
    _tokenRefreshManager = tokenRefreshManager;
    _isInitialized = true;
    
    log("✅ DynamicTokenScheduler initialized");
  }
  
  /// Start dynamic token scheduling
  Future<void> startDynamicScheduling() async {
    if (!_isInitialized) {
      throw StateError('DynamicTokenScheduler not initialized');
    }
    
    if (_isActive) {
      log("⚠️ Dynamic token scheduling already active");
      return;
    }
    
    try {
      log("🔄 Starting dynamic token scheduling");
      
      // Perform initial token analysis
      await _performInitialTokenAnalysis();
      
      // Start monitoring timer
      _startMonitoringTimer();
      
      _isActive = true;
      log("✅ Dynamic token scheduling started");
    } catch (e) {
      log("❌ Error starting dynamic token scheduling: $e");
      rethrow;
    }
  }
  
  /// Stop dynamic token scheduling
  void stopDynamicScheduling() {
    if (!_isActive) return;
    
    log("🛑 Stopping dynamic token scheduling");
    
    _accessTokenRefreshTimer?.cancel();
    _refreshTokenRefreshTimer?.cancel();
    _monitoringTimer?.cancel();
    
    _accessTokenRefreshTimer = null;
    _refreshTokenRefreshTimer = null;
    _monitoringTimer = null;
    _nextAccessTokenRefresh = null;
    _nextRefreshTokenRefresh = null;
    
    _isActive = false;
    log("✅ Dynamic token scheduling stopped");
  }
  
  /// Perform initial token analysis and scheduling
  Future<void> _performInitialTokenAnalysis() async {
    try {
      // Get current tokens
      final accessToken = await _tokenRefreshManager!.getValidAccessToken();
      final refreshToken = await _getRefreshToken();
      
      if (accessToken != null) {
        await _analyzeAndScheduleAccessToken(accessToken);
      }
      
      if (refreshToken != null) {
        await _analyzeAndScheduleRefreshToken(refreshToken);
      }
      
    } catch (e) {
      log("❌ Error during initial token analysis: $e");
    }
  }
  
  /// Analyze and schedule access token refresh
  Future<void> _analyzeAndScheduleAccessToken(String accessToken) async {
    try {
      // Skip if same token already analyzed
      if (_currentAccessToken == accessToken) return;
      
      _currentAccessToken = accessToken;
      
      // Get token analysis
      final analysis = JwtTokenUtility.getTokenAnalysis(accessToken);
      final refreshTime = JwtTokenUtility.getOptimalRefreshTime(accessToken);
      
      if (refreshTime == null) {
        log("⚠️ Could not determine access token refresh time");
        return;
      }
      
      // Cancel existing timer
      _accessTokenRefreshTimer?.cancel();
      _nextAccessTokenRefresh = refreshTime;
      
      final now = DateTime.now();
      final delay = refreshTime.difference(now);
      
      if (delay.isNegative || delay.inSeconds <= 0) {
        log("🔄 Access token refresh needed immediately");
        Timer(const Duration(milliseconds: 100), () => _refreshAccessToken());
        return;
      }
      
      log("⏰ Access token refresh scheduled in ${delay.inMinutes}m ${delay.inSeconds % 60}s");
      log("📊 Token lifespan: ${analysis['lifespanMinutes']} minutes");
      log("🔄 Refresh buffer: ${analysis['refreshBuffer']} minutes");
      
      _accessTokenRefreshTimer = Timer(delay, () => _refreshAccessToken());
      
    } catch (e) {
      log("❌ Error analyzing access token: $e");
    }
  }
  
  /// Analyze and schedule refresh token refresh (if it's a JWT)
  Future<void> _analyzeAndScheduleRefreshToken(String refreshToken) async {
    try {
      // Only schedule refresh for JWT refresh tokens
      if (!refreshToken.contains('.')) {
        log("ℹ️ Refresh token is not JWT, skipping dynamic scheduling");
        return;
      }
      
      // Skip if same token already analyzed
      if (_currentRefreshToken == refreshToken) return;
      
      _currentRefreshToken = refreshToken;
      
      // Calculate refresh time (refresh 4 minutes before expiry)
      final expirationTime = JwtTokenUtility.getTokenExpirationTime(refreshToken);
      if (expirationTime == null) {
        log("⚠️ Could not determine refresh token expiration time");
        return;
      }
      
      final refreshTime = expirationTime.subtract(const Duration(minutes: 4));
      
      // Cancel existing timer
      _refreshTokenRefreshTimer?.cancel();
      _nextRefreshTokenRefresh = refreshTime;
      
      final now = DateTime.now();
      final delay = refreshTime.difference(now);
      
      if (delay.isNegative || delay.inSeconds <= 0) {
        log("🔄 Refresh token refresh needed immediately");
        Timer(const Duration(milliseconds: 100), () => _refreshRefreshToken());
        return;
      }
      
      log("⏰ Refresh token refresh scheduled in ${delay.inMinutes}m ${delay.inSeconds % 60}s");
      
      _refreshTokenRefreshTimer = Timer(delay, () => _refreshRefreshToken());
      
    } catch (e) {
      log("❌ Error analyzing refresh token: $e");
    }
  }
  
  /// Start monitoring timer for continuous token analysis
  void _startMonitoringTimer() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(_monitoringInterval, (_) => _performPeriodicCheck());
    log("👁️ Token monitoring started (interval: ${_monitoringInterval.inSeconds}s)");
  }
  
  /// Perform periodic check for token changes
  Future<void> _performPeriodicCheck() async {
    try {
      // Get current tokens
      final accessToken = await _tokenRefreshManager!.getValidAccessToken();
      final refreshToken = await _getRefreshToken();
      
      // Check if access token changed
      if (accessToken != null && accessToken != _currentAccessToken) {
        log("🔄 New access token detected, updating schedule");
        await _analyzeAndScheduleAccessToken(accessToken);
      }
      
      // Check if refresh token changed
      if (refreshToken != null && refreshToken != _currentRefreshToken) {
        log("🔄 New refresh token detected, updating schedule");
        await _analyzeAndScheduleRefreshToken(refreshToken);
      }
      
      // Check if scheduled refreshes are due
      final now = DateTime.now();
      
      if (_nextAccessTokenRefresh != null && now.isAfter(_nextAccessTokenRefresh!)) {
        log("⏰ Scheduled access token refresh is due");
        await _refreshAccessToken();
      }
      
      if (_nextRefreshTokenRefresh != null && now.isAfter(_nextRefreshTokenRefresh!)) {
        log("⏰ Scheduled refresh token refresh is due");
        await _refreshRefreshToken();
      }
      
    } catch (e) {
      log("❌ Error during periodic check: $e");
    }
  }
  
  /// Refresh access token
  Future<void> _refreshAccessToken() async {
    try {
      log("🔄 Executing scheduled access token refresh");
      final success = await _tokenRefreshManager!.refreshTokenIfNeeded();
      
      if (success) {
        log("✅ Scheduled access token refresh successful");
        // New token will be analyzed in the next periodic check
      } else {
        log("❌ Scheduled access token refresh failed");
      }
    } catch (e) {
      log("❌ Error during scheduled access token refresh: $e");
    }
  }
  
  /// Refresh refresh token (placeholder for future implementation)
  Future<void> _refreshRefreshToken() async {
    try {
      log("🔄 Refresh token refresh would be executed here");
      // This would require server-side support for refresh token rotation
      log("ℹ️ Refresh token refresh not implemented - requires server support");
    } catch (e) {
      log("❌ Error during refresh token refresh: $e");
    }
  }
  
  /// Get current refresh token (helper method)
  Future<String?> _getRefreshToken() async {
    try {
      // This would need to be implemented based on your secure storage setup
      // For now, return null as we don't have direct access to secure storage
      return null;
    } catch (e) {
      log("❌ Error getting refresh token: $e");
      return null;
    }
  }
  
  /// Get current scheduling status
  Map<String, dynamic> getSchedulingStatus() {
    return {
      'isInitialized': _isInitialized,
      'isActive': _isActive,
      'nextAccessTokenRefresh': _nextAccessTokenRefresh?.toIso8601String(),
      'nextRefreshTokenRefresh': _nextRefreshTokenRefresh?.toIso8601String(),
      'hasAccessTokenTimer': _accessTokenRefreshTimer != null,
      'hasRefreshTokenTimer': _refreshTokenRefreshTimer != null,
      'hasMonitoringTimer': _monitoringTimer != null,
      'monitoringIntervalSeconds': _monitoringInterval.inSeconds,
    };
  }
  
  /// Force immediate token analysis
  Future<void> forceTokenAnalysis() async {
    if (!_isActive) {
      log("⚠️ Dynamic token scheduling not active");
      return;
    }
    
    log("🔄 Forcing immediate token analysis");
    await _performInitialTokenAnalysis();
  }
  
  /// Dispose resources
  void dispose() {
    stopDynamicScheduling();
    _isInitialized = false;
    log("✅ DynamicTokenScheduler disposed");
  }
}
